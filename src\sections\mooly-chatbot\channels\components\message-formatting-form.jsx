'use client';

import PropTypes from 'prop-types';
import { useState, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Switch from '@mui/material/Switch';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import FormControlLabel from '@mui/material/FormControlLabel';
import { alpha, useTheme } from '@mui/material/styles';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import CompactImageUpload from './compact-image-upload';

import {
  MESSAGE_TYPES,
  BUTTON_TYPES,
  FORMATTING_LIMITS,
  BUTTON_TYPE_OPTIONS,
  MESSAGE_TEMPLATES_FORMATTED,
  createButton,
  validateMessageFormatting,
  generateMessagePreview
} from 'src/actions/mooly-chatbot/message-formatting-service';

// ----------------------------------------------------------------------

export default function MessageFormattingForm({ 
  value, 
  onChange, 
  disabled = false,
  showTemplates = true 
}) {
  const theme = useTheme();
  
  // State cho form
  const [messageType, setMessageType] = useState(value?.messageType || MESSAGE_TYPES.TEXT);
  const [content, setContent] = useState(value?.message || '');
  const [images, setImages] = useState(value?.messageFormatting?.images || []);
  const [buttons, setButtons] = useState(value?.messageFormatting?.buttons || []);
  
  // State cho button form
  const [showButtonForm, setShowButtonForm] = useState(false);
  const [editingButton, setEditingButton] = useState(null);
  const [buttonForm, setButtonForm] = useState({
    title: '',
    type: BUTTON_TYPES.LINK,
    value: ''
  });

  // Update parent component
  const updateParent = useCallback(() => {
    const messageData = {
      message: content,
      messageType,
      messageFormatting: messageType === MESSAGE_TYPES.FORMATTED ? { images, buttons } : null
    };
    onChange?.(messageData);
  }, [content, messageType, images, buttons, onChange]);

  // Handle message type change
  const handleMessageTypeChange = (newType) => {
    setMessageType(newType);
    if (newType === MESSAGE_TYPES.TEXT) {
      setImages([]);
      setButtons([]);
    }
    setTimeout(updateParent, 0);
  };

  // Handle content change
  const handleContentChange = (event) => {
    setContent(event.target.value);
    setTimeout(updateParent, 0);
  };

  // Handle image change from CompactImageUpload
  const handleImageChange = useCallback((newImages) => {
    setImages(newImages);
    setTimeout(updateParent, 0);
  }, [updateParent]);

  // Handle button form
  const resetButtonForm = () => {
    setButtonForm({ title: '', type: BUTTON_TYPES.LINK, value: '' });
    setEditingButton(null);
    setShowButtonForm(false);
  };

  const handleAddButton = () => {
    if (buttons.length >= FORMATTING_LIMITS.MAX_BUTTONS) {
      toast.error(`Chỉ được tạo tối đa ${FORMATTING_LIMITS.MAX_BUTTONS} nút bấm`);
      return;
    }
    resetButtonForm();
    setShowButtonForm(true);
  };

  const handleEditButton = (button) => {
    setButtonForm({
      title: button.title,
      type: button.type,
      value: button.url || button.payload || ''
    });
    setEditingButton(button);
    setShowButtonForm(true);
  };

  const handleSaveButton = () => {
    if (!buttonForm.title.trim()) {
      toast.error('Tiêu đề nút không được để trống');
      return;
    }

    if (!buttonForm.value.trim()) {
      toast.error(buttonForm.type === BUTTON_TYPES.LINK ? 'URL không được để trống' : 'Payload không được để trống');
      return;
    }

    if (editingButton) {
      // Update existing button
      const updatedButtons = buttons.map(btn =>
        btn.id === editingButton.id
          ? createButton(buttonForm.title, buttonForm.type, buttonForm.value)
          : btn
      );
      setButtons(updatedButtons);
    } else {
      // Add new button
      const newButton = createButton(buttonForm.title, buttonForm.type, buttonForm.value);
      setButtons([...buttons, newButton]);
    }

    resetButtonForm();
    setTimeout(updateParent, 0);
  };

  const handleDeleteButton = (buttonId) => {
    const updatedButtons = buttons.filter(btn => btn.id !== buttonId);
    setButtons(updatedButtons);
    setTimeout(updateParent, 0);
  };

  // Handle template
  const handleUseTemplate = (template) => {
    setContent(template.content);
    setImages(template.images || []);
    setButtons(template.buttons || []);
    setMessageType(MESSAGE_TYPES.FORMATTED);
    setTimeout(updateParent, 0);
  };

  return (
    <Stack spacing={2}>
      {/* Message Type Toggle */}
      <Box>
        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
          Loại tin nhắn
        </Typography>
        <Stack direction="row" spacing={1}>
          <Chip
            label="Tin nhắn thường"
            variant={messageType === MESSAGE_TYPES.TEXT ? 'filled' : 'outlined'}
            color={messageType === MESSAGE_TYPES.TEXT ? 'primary' : 'default'}
            onClick={() => handleMessageTypeChange(MESSAGE_TYPES.TEXT)}
            disabled={disabled}
            size="small"
          />
          <Chip
            label="Tin nhắn định dạng"
            variant={messageType === MESSAGE_TYPES.FORMATTED ? 'filled' : 'outlined'}
            color={messageType === MESSAGE_TYPES.FORMATTED ? 'primary' : 'default'}
            onClick={() => handleMessageTypeChange(MESSAGE_TYPES.FORMATTED)}
            disabled={disabled}
            size="small"
          />
        </Stack>
      </Box>

      {/* Message Content */}
      <TextField
        multiline
        rows={3}
        label="Nội dung tin nhắn"
        value={content}
        onChange={handleContentChange}
        placeholder="Nhập nội dung tin nhắn..."
        disabled={disabled}
        size="small"
        slotProps={{
          htmlInput: {
            maxLength: FORMATTING_LIMITS.MAX_MESSAGE_LENGTH
          }
        }}
        helperText={`${content.length}/${FORMATTING_LIMITS.MAX_MESSAGE_LENGTH} ký tự`}
      />

      {/* Formatted Message Options */}
      {messageType === MESSAGE_TYPES.FORMATTED && (
        <Stack spacing={2}>
          {/* Images Section */}
          <CompactImageUpload
            images={images}
            onChange={handleImageChange}
            disabled={disabled}
            maxImages={FORMATTING_LIMITS.MAX_IMAGES}
          />

          {/* Buttons Section */}
          <Box>
            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                Nút bấm ({buttons.length}/{FORMATTING_LIMITS.MAX_BUTTONS})
              </Typography>
              <Button
                size="small"
                variant="outlined"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={handleAddButton}
                disabled={disabled || buttons.length >= FORMATTING_LIMITS.MAX_BUTTONS}
              >
                Thêm nút
              </Button>
            </Stack>

            {/* Buttons List */}
            {buttons.length > 0 && (
              <Stack spacing={1} mb={1}>
                {buttons.map((button) => (
                  <Card
                    key={button.id}
                    sx={{
                      p: 1.5,
                      border: `1px solid ${alpha(theme.palette.grey[500], 0.3)}`
                    }}
                  >
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Iconify 
                        icon={BUTTON_TYPE_OPTIONS.find(opt => opt.value === button.type)?.icon || 'eva:link-fill'} 
                        width={16} 
                      />
                      <Box flexGrow={1}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {button.title}
                        </Typography>
                        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                          {button.type === BUTTON_TYPES.LINK ? button.url : button.payload}
                        </Typography>
                      </Box>
                      <Stack direction="row" spacing={0.5}>
                        <IconButton
                          size="small"
                          onClick={() => handleEditButton(button)}
                          disabled={disabled}
                        >
                          <Iconify icon="eva:edit-fill" width={16} />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteButton(button.id)}
                          disabled={disabled}
                        >
                          <Iconify icon="eva:trash-2-fill" width={16} />
                        </IconButton>
                      </Stack>
                    </Stack>
                  </Card>
                ))}
              </Stack>
            )}

            {/* Button Form */}
            {showButtonForm && (
              <Card sx={{ p: 2, border: `2px dashed ${theme.palette.primary.main}` }}>
                <Typography variant="subtitle2" mb={2}>
                  {editingButton ? 'Chỉnh sửa nút' : 'Thêm nút mới'}
                </Typography>

                <Stack spacing={2}>
                  <TextField
                    label="Tiêu đề nút"
                    value={buttonForm.title}
                    onChange={(e) => setButtonForm(prev => ({ ...prev, title: e.target.value }))}
                    size="small"
                    slotProps={{
                      htmlInput: {
                        maxLength: FORMATTING_LIMITS.MAX_BUTTON_TITLE_LENGTH
                      }
                    }}
                    helperText={`${buttonForm.title.length}/${FORMATTING_LIMITS.MAX_BUTTON_TITLE_LENGTH} ký tự`}
                  />

                  <TextField
                    select
                    label="Loại nút"
                    value={buttonForm.type}
                    onChange={(e) => setButtonForm(prev => ({ ...prev, type: e.target.value, value: '' }))}
                    size="small"
                  >
                    {BUTTON_TYPE_OPTIONS.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify icon={option.icon} width={16} />
                          <span>{option.label}</span>
                        </Stack>
                      </MenuItem>
                    ))}
                  </TextField>

                  <TextField
                    label={buttonForm.type === BUTTON_TYPES.LINK ? 'URL' : 'Payload'}
                    value={buttonForm.value}
                    onChange={(e) => setButtonForm(prev => ({ ...prev, value: e.target.value }))}
                    placeholder={buttonForm.type === BUTTON_TYPES.LINK ? 'https://example.com' : 'ACTION_NAME'}
                    size="small"
                  />

                  <Stack direction="row" spacing={1} justifyContent="flex-end">
                    <Button size="small" onClick={resetButtonForm}>
                      Hủy
                    </Button>
                    <Button
                      size="small"
                      variant="contained"
                      onClick={handleSaveButton}
                      disabled={!buttonForm.title.trim() || !buttonForm.value.trim()}
                    >
                      {editingButton ? 'Cập nhật' : 'Thêm'}
                    </Button>
                  </Stack>
                </Stack>
              </Card>
            )}
          </Box>
        </Stack>
      )}

      {/* Templates */}
      {showTemplates && messageType === MESSAGE_TYPES.FORMATTED && (
        <Box>
          <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
            Hoặc chọn template có sẵn:
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {MESSAGE_TEMPLATES_FORMATTED.map((template, index) => (
              <Chip
                key={index}
                label={`${template.content.substring(0, 20)}... [${template.buttons?.length || 0} nút]`}
                size="small"
                variant="outlined"
                onClick={() => handleUseTemplate(template)}
                sx={{ cursor: 'pointer' }}
                disabled={disabled}
              />
            ))}
          </Stack>
        </Box>
      )}
    </Stack>
  );
}

MessageFormattingForm.propTypes = {
  value: PropTypes.object,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  showTemplates: PropTypes.bool,
};
